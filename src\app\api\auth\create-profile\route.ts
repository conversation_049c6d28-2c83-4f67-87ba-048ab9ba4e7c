import { NextResponse } from "next/server";
import { supabase as supabaseServerClient } from "@/lib/supabaseClient";
import { ApprovalStatus } from "@/types/types";

export const POST = async (req: Request) => {
  try {
    const body = await req.json();
    const { userId, email, name, role = 'client' } = body;

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'User ID and email are required' }, 
        { status: 400 }
      );
    }

    // Check if profile already exists
    const { data: existingProfile, error: checkError } = await supabaseServerClient
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new users
      throw checkError;
    }

    if (existingProfile) {
      return NextResponse.json(
        { error: 'Profile already exists' }, 
        { status: 409 }
      );
    }

    // Create new profile with pending approval status
    const { data, error } = await supabaseServerClient
      .from('profiles')
      .insert([{
        id: userId,
        email: email,
        name: name || null,
        role: role,
        approval_status: ApprovalStatus.Pending,
        status: 'active',
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json(
      { 
        message: 'Profile created successfully', 
        profile: data,
        approval_status: ApprovalStatus.Pending
      }, 
      { status: 201 }
    );

  } catch (error: any) {
    console.error('Error creating profile:', error.message);
    return NextResponse.json(
      { error: error.message }, 
      { status: 500 }
    );
  }
};
