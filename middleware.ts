import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";
import { ApprovalStatus } from "@/types/types";

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: "",
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: "",
            ...options,
          });
        },
      },
    }
  );

  // Get the current path
  const path = request.nextUrl.pathname;

  // Public routes that don't require authentication
  const publicRoutes = ["/login", "/signup", "/", "/auth"];
  
  // Routes that require authentication but not approval
  const authOnlyRoutes = ["/pending-approval"];

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route => path.startsWith(route));
  const isAuthOnlyRoute = authOnlyRoutes.some(route => path.startsWith(route));

  try {
    // Get the current session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("Session error:", sessionError);
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // If no session and trying to access protected route
    if (!session && !isPublicRoute) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // If session exists, check approval status for protected routes
    if (session && !isPublicRoute && !isAuthOnlyRoute) {
      try {
        // Get user profile with approval status
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("approval_status, role")
          .eq("id", session.user.id)
          .single();

        if (profileError) {
          console.error("Profile error:", profileError);
          // If profile doesn't exist, redirect to login
          return NextResponse.redirect(new URL("/login", request.url));
        }

        // Check approval status
        const approvalStatus = profile?.approval_status || ApprovalStatus.Pending;
        
        if (approvalStatus === ApprovalStatus.Pending) {
          // User is not approved, redirect to pending approval page
          return NextResponse.redirect(new URL("/pending-approval", request.url));
        }

        // If user is approved but trying to access pending approval page, redirect to dashboard
        if (approvalStatus === ApprovalStatus.Approved && path === "/pending-approval") {
          return NextResponse.redirect(new URL("/dashboard", request.url));
        }

      } catch (error) {
        console.error("Error checking approval status:", error);
        return NextResponse.redirect(new URL("/login", request.url));
      }
    }

    // If user is authenticated and trying to access login page, redirect based on approval status
    if (session && path === "/login") {
      try {
        const { data: profile } = await supabase
          .from("profiles")
          .select("approval_status")
          .eq("id", session.user.id)
          .single();

        const approvalStatus = profile?.approval_status || ApprovalStatus.Pending;
        
        if (approvalStatus === ApprovalStatus.Pending) {
          return NextResponse.redirect(new URL("/pending-approval", request.url));
        } else if (approvalStatus === ApprovalStatus.Approved) {
          return NextResponse.redirect(new URL("/dashboard", request.url));
        }
      } catch (error) {
        console.error("Error checking approval status on login:", error);
      }
    }

    return response;

  } catch (error) {
    console.error("Middleware error:", error);
    return NextResponse.redirect(new URL("/login", request.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
