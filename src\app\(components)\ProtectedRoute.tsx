import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'
import type { UserRole } from '@/context/AuthContext'
import { ApprovalStatus } from '@/types/types'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
  requireApproval?: boolean
}

const ProtectedRoute = ({ children, allowedRoles, requireApproval = true }: ProtectedRouteProps) => {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      // If no authenticated user, redirect to login
      if (!user) {
        router.push('/login')
        return
      }

      // Check approval status if required
      if (requireApproval && user.approval_status === ApprovalStatus.Pending) {
        router.push('/pending-approval')
        return
      }

      // If there are allowed roles specified and user doesn't have one of those roles
      if (allowedRoles && !allowedRoles.includes(user.role)) {
        // Redirect based on user role
        if (user.role === 'client') {
          router.push('/menu')
        } else {
          router.push('/dashboard')
        }
        return
      }
    }
  }, [user, loading, router, allowedRoles, requireApproval])

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  // If no user or user doesn't have allowed role, show nothing
  if (!user || (allowedRoles && !allowedRoles.includes(user.role))) {
    return null
  }

  // If approval is required and user is pending, show nothing
  if (requireApproval && user.approval_status === ApprovalStatus.Pending) {
    return null
  }

  // If everything is good, show the content
  return <>{children}</>
}

export default ProtectedRoute