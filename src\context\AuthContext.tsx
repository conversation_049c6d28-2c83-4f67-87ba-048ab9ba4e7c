"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabaseClient";
import { ApprovalStatus } from "@/types/types";

export type UserRole = "client" | "admin" | "master" | "barman";

interface UserWithRole {
  id: string;
  email: string;
  role: UserRole;
  approval_status: ApprovalStatus;
}

interface AuthContextType {
  user: UserWithRole | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signOut: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<UserWithRole | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const getUserProfile = useCallback(async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("role, approval_status")
        .eq("id", userId)
        .single();

      if (error) throw error;
      return {
        role: data?.role as UserRole,
        approval_status: data?.approval_status as ApprovalStatus || ApprovalStatus.Pending
      };
    } catch (error) {
      console.error("Error fetching user profile:", error);
      return null;
    }
  }, []);



  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
      setUser(null);
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setLoading(false);
    }
  }, [router]);

  const initializeAuth = useCallback(async () => {
    try {
      setLoading(true);
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session) {
        const profile = await getUserProfile(session.user.id);
        if (profile) {
          setUser({
            id: session.user.id,
            email: session.user.email || "",
            role: profile.role,
            approval_status: profile.approval_status,
          });

          // Check approval status and redirect accordingly
          if (profile.approval_status === ApprovalStatus.Pending) {
            if (window.location.pathname !== "/pending-approval") {
              router.push("/pending-approval");
            }
          } else if (profile.approval_status === ApprovalStatus.Approved) {
            if (window.location.pathname === "/login" || window.location.pathname === "/pending-approval") {
              router.push("/dashboard");
            }
          }
        }
      } else {
        router.push("/login");
      }
    } catch (error) {
      console.error("Auth initialization error:", error);
      router.push("/login");
    } finally {
      setLoading(false);
    }
  }, [getUserProfile, router]);

  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "SIGNED_IN" && session?.user?.id) {
        const profile = await getUserProfile(session.user.id);
        if (profile) {
          setUser({
            id: session.user.id,
            email: session.user.email || "",
            role: profile.role,
            approval_status: profile.approval_status,
          });

          // Check approval status and redirect accordingly
          if (profile.approval_status === ApprovalStatus.Pending) {
            router.push("/pending-approval");
          } else if (profile.approval_status === ApprovalStatus.Approved) {
            if (window.location.pathname === "/login" || window.location.pathname === "/pending-approval") {
              router.push("/dashboard");
            }
          }
        }
      } else if (event === "SIGNED_OUT") {
        setUser(null);
        router.push("/login");
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [getUserProfile, router]);

  return (
    <AuthContext.Provider value={{ user, loading, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};
